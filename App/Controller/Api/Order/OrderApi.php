<?php

namespace App\Controller\Api\Order;

use App\Model\OrderModel;
use App\Service\HyCompaniesService;
use App\Service\OrderService;
use Generate\Models\Datas\AddressModel;
use Generate\Tables\Datas\AddressTable;
use Generate\Tables\Datas\OrderTable;
use Generate\Tables\Datas\ShopCarTable;
use Generate\Tables\Datas\ShopOrderTable;
use Generate\Tables\Datas\ShopProductsSkuTable;
use Generate\Tables\Datas\ShopProductsTable;
use Generate\Tables\Datas\HyCompaniesServiceTable;
use Generate\Tables\Datas\HyProductsSkuTable;
use Generate\Tables\Datas\OrderLogisticsTable;
use Protobuf\Datas\Order\OrderListsProto;
use Protobuf\Datas\Order\OrderProto;
use Protobuf\Datas\ShopOrder\ShopOrderProto;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Queue\MessageQueue;
use Swlib\Router\Router;
use Swlib\Table\Db;
use Protobuf\Common\Success;
use Protobuf\Datas\Order\OrderStatusEnum;
use Swlib\Utils\Server;
use Throwable;

#[Router(method: 'POST')]
class OrderApi extends AbstractController
{
    private OrderService $orderService;

    public function __construct()
    {
        $this->orderService = new OrderService();
    }

    /**
     * 获取订单列表，按照订单ID 分组
     * @throws Throwable
     */
    #[Router(errorTitle: '获取订单列表失败')]
    public function orders(OrderProto $request): OrderListsProto
    {

        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $userId = $request->getUserId();
        $status = $request->getStatus();
        $keyword = $request->getExtStr();

        if (empty($userId)) {
            throw new AppException("请登录");
        }

        $andWhere = [
            [OrderTable::USER_ID, '=', $userId],
        ];

        // 添加状态查询条件
        $statusCondition = $this->orderService->buildStatusCondition($status);
        if ($statusCondition) {
            $andWhere[] = $statusCondition;
        }

        if ($keyword) {
            $sns = new ShopOrderTable()->whereOr([
                [ShopOrderTable::PRODUCT_NAME, 'like', "%$keyword%"],
                [ShopOrderTable::SKU_NAME, 'like', "%$keyword%"],
            ])->getArrayByField(ShopOrderTable::SN);
            $andWhere[] = [ShopProductsTable::NAME, 'IN', $sns];
        }

        // 查询出分页的订单sn
        $table = new OrderTable();
        $orders = $table
            ->field(OrderTable::FIELD_ALL)
            ->page($page, $size)
            ->where($andWhere)
            ->order([
                OrderTable::TIME => 'DESC'
            ])
            ->selectAll();

        $sns = $table->getArrayByField(OrderTable::SN);

        // 获取订单商品详情
        $shopOrderProtoLists = $this->orderService->getOrderShopDetails($sns);

        return $this->orderService->formatLists($orders, $shopOrderProtoLists);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '商家查询订单信息')]
    public function ordersByBusiness(OrderProto $request): OrderListsProto
    {
        $businessId = $request->getExtInt();
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;
        $status = $request->getStatus();

        if (empty($businessId)) {
            throw new AppException("商家ID不能为空");
        }


        // 使用 JOIN 查询获取属于该商家的订单
        $orderTable = new OrderTable();
        $query = $orderTable
            ->join(ShopOrderTable::TABLE_NAME, ShopOrderTable::SN, OrderTable::SN)
            ->page($page, $size)
            ->where([
                [ShopOrderTable::BUSINESS_ID, '=', $businessId],
            ])
            ->order([
                OrderTable::TIME => 'DESC'
            ])
            ->field(OrderTable::FIELD_ALL)
            ->distinct();


        if ($status === OrderStatusEnum::WAIT_DELIVERY) {
            // 待发货
//            $query->addWhere(OrderTable::STATUS, [
//                \Generate\Models\Datas\OrderModel::StatusWait_delivery,
//                \Generate\Models\Datas\OrderModel::StatusPartial_shipment
//            ], 'in');
            $query->addWhereRaw(' and  `order`.`status` not in ("wait_pay","timeout","") and  `shop_order`.`quantity_shipped` < `shop_order`.`num`');
        } else if ($status === OrderStatusEnum::WAIT_RECEIVE) {
            // 待收货
            $query->addWhere(OrderTable::STATUS, \Generate\Models\Datas\OrderModel::StatusWait_receive);
        }

        $orders = $query->setDebugSql()->selectAll();
        $sns = $orderTable->getArrayByField(OrderTable::SN);


        // 获取订单商品详情（只获取该商家的商品）
        $shopOrderProtoLists = $this->orderService->getOrderShopDetails($sns, $businessId);

        return $this->orderService->formatLists($orders, $shopOrderProtoLists);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '用户申请退款')]
    public function applyForReturn(OrderProto $request): Success
    {
        $id = $request->getId();
        $sn = $request->getSn();

        $where = [];
        if ($id) {
            $where[OrderTable::ID] = $id;
        }
        if ($sn) {
            $where[OrderTable::SN] = $sn;
        }

        if (empty($where)) {
            throw new AppException("请选择订单");
        }

        $order = new OrderTable()->where($where)->selectOne();

        if (empty($order)) {
            throw new AppException("订单不存在");
        }


        // 待发货 待收货 才可以退款
        if (!in_array($order->status, [\Generate\Models\Datas\OrderModel::StatusWait_delivery, \Generate\Models\Datas\OrderModel::StatusWait_receive])) {
            throw new AppException("订单状态不支持退款");
        }

        new OrderTable()->where([
            [OrderTable::ID, '=', $id]
        ])->update([
            OrderTable::STATUS => \Generate\Models\Datas\OrderModel::StatusRefund,
            OrderTable::REFUND_TIME => time(),
        ]);

        $success = new Success();
        $success->setSuccess(true);
        return $success;

    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '商家确认退款')]
    public function companiesConfirmForReturn(OrderProto $request): Success
    {
        $sn = $request->getSn();
        $userId = $request->getUserId();
        if (empty($sn) && empty($userId)) {
            throw new AppException("请选择订单");
        }


        $order = new OrderTable()->where([
            [OrderTable::SN, '=', $sn],
            [OrderTable::USER_ID, '=', $userId],
        ])->selectOne();

        if (empty($order)) {
            throw new AppException("订单不存在");
        }


        // 待发货 待收货 才可以退款
        if ($order->status != \Generate\Models\Datas\OrderModel::StatusRefund) {
            throw new AppException("订单状态不支持确认退款");
        }

        $res = Db::transaction(function () use ($order) {
            // 增加回去库存
            foreach (new ShopOrderTable()->where([
                [ShopOrderTable::ORDER_ID, '=', $order->id]
            ])->selectAll() as $shopOrderTable) {
                $incrRES = new HyProductsSkuTable()->where([
                    HyProductsSkuTable::ID => $shopOrderTable->skuId,
                ])->update([
                    HyProductsSkuTable::INVENTORY => Db::incr(HyProductsSkuTable::INVENTORY, $shopOrderTable->num)
                ]);
                if (empty($incrRES)) {
                    throw new AppException("库存修改失败");
                }
            }


            // 修改状态
            return new OrderTable()->where([
                [OrderTable::ID, '=', $order->id]
            ])->update([
                OrderTable::STATUS => \Generate\Models\Datas\OrderModel::StatusRefund_finish,
                OrderTable::REFUND_TIME => time(),
            ]);
        });


        $success = new Success();
        $success->setSuccess((bool)$res);
        return $success;

    }


    /**
     * 从购物车发起结算
     * @throws Throwable
     */
    #[Router(errorTitle: '添加订单失败')]
    public function addOrderByCar(OrderProto $request): ShopOrderProto
    {
        $skuIdString = $request->getExtStr();
        $userId = $request->getUserId();
        $addrId = $request->getAddrId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }
        if (empty($addrId)) {
            throw new AppException('请选择收货地址');
        }


        $skuIds = [];
        foreach (explode(',', $skuIdString) as $id) {
            $skuIds[] = $id;
        }

        $cars = new ShopCarTable()
            ->where([
                [ShopCarTable::USER_ID, '=', $userId],
                [ShopCarTable::SKU_ID, 'in', $skuIds],
            ])
            ->join(ShopProductsSkuTable::TABLE_NAME, ShopCarTable::SKU_ID, ShopProductsSkuTable::ID)
            ->field([
                ShopCarTable::ID,
                ShopCarTable::BUSINESS_ID,
                ShopCarTable::PRODUCT_ID,
                ShopCarTable::SKU_ID,
                ShopCarTable::NUM,
                ShopProductsSkuTable::PRICE
            ])
            ->selectAll();


        $message = Db::transaction(function () use ($cars, $userId, $addrId) {
            // 创建订单号
            $sn = OrderModel::createOrderSn();

            $time = time();

            $orderTotalPrice = 0;
            foreach ($cars as $info) {
                $price = $info->getByField(ShopProductsSkuTable::PRICE);
                $num = $info->num;
                $orderTotalPrice += $price * $num;
            }


            // 写入订单总表
            $orderId = new OrderTable()->insert([
                OrderTable::SN => $sn,
                OrderTable::ORIGINAL_PRICE => $orderTotalPrice,
                OrderTable::PRICE => $orderTotalPrice,
                OrderTable::USER_ID => $userId,
                OrderTable::TIME => $time,
                OrderTable::ADDR_ID => $addrId,
                OrderTable::STATUS => \Generate\Models\Datas\OrderModel::StatusWait_pay,
            ]);


            // 订单明细
            $shopOrders = [];
            $carIds = [];
            foreach ($cars as $info) {
                $price = $info->getByField(ShopProductsSkuTable::PRICE);
                $num = $info->num;
                $shopOrders[] = [
                    ShopOrderTable::USER_ID => $userId,
                    ShopOrderTable::SN => $sn,
                    ShopOrderTable::PRICE => $price,
                    ShopOrderTable::NUM => $num,
                    ShopOrderTable::BUSINESS_ID => $info->businessId ?: 1,
                    ShopOrderTable::PRODUCT_ID => $info->productId,
                    ShopOrderTable::SKU_ID => $info->skuId,
                    ShopOrderTable::TIME => $time,
                    ShopOrderTable::ORDER_ID => $orderId,
                ];
                $carIds[] = $info->id;
            }

            // 写入订单明细
            $insertDetailRet = new ShopOrderTable()->insertAll($shopOrders);

            $delRet = new ShopCarTable()->where([
                [ShopCarTable::ID, 'in', $carIds]
            ])->delete();

            if ($insertDetailRet && $orderId && $delRet) {
                $message = new ShopOrderProto();
                $message->setSn($sn);
                $message->setPrice($orderTotalPrice);
                return $message;
            }
            return false;
        });

        if (empty($message)) {
            throw new AppException('添加订单失败');
        }

        return $message;
    }


    /**
     * 在商品详情页 立即购买
     * @throws Throwable
     */
    #[Router(errorTitle: '添加订单失败')]
    public function buyNow(OrderProto $request): ShopOrderProto
    {
        $skuId = $request->getExtInt();
        $userId = $request->getUserId();
        $addrId = $request->getAddrId();
        if (empty($userId)) {
            throw new AppException('请登录');
        }
        if (empty($addrId)) {
            throw new AppException('请选择收货地址');
        }

        /** @var $skuTable ShopProductsSkuTable */
        $skuTable = new ShopProductsSkuTable()
            ->where([
                [ShopProductsSkuTable::ID, '=', $skuId],
            ])
            ->join(ShopProductsTable::TABLE_NAME, ShopProductsTable::ID, ShopProductsSkuTable::PRODUCT_ID)
            ->field([
                ShopProductsSkuTable::ID,
                ShopProductsSkuTable::PRODUCT_ID,
                ShopProductsSkuTable::PRICE,
                ShopProductsTable::BUSINESS_ID,
                ShopProductsTable::NAME,
            ])
            ->selectOne();
        if (empty($skuTable)) {
            throw new AppException('商品不存在');
        }


        $message = Db::transaction(function () use ($skuTable, $userId, $addrId) {
            // 创建订单号
            $sn = OrderModel::createOrderSn();

            $time = time();

            $price = $skuTable->price;
            $num = 1;
            $orderTotalPrice = $price * $num;

            // 写入订单总表
            $orderId = new OrderTable()->insert([
                OrderTable::SN => $sn,
                OrderTable::ORIGINAL_PRICE => $orderTotalPrice,
                OrderTable::PRICE => $orderTotalPrice,
                OrderTable::USER_ID => $userId,
                OrderTable::TIME => $time,
                OrderTable::ADDR_ID => $addrId,
                OrderTable::STATUS => \Generate\Models\Datas\OrderModel::StatusWait_pay
            ]);


            // 订单明细
            $shopOrders[] = [
                ShopOrderTable::USER_ID => $userId,
                ShopOrderTable::SN => $sn,
                ShopOrderTable::PRICE => $price,
                ShopOrderTable::NUM => $num,
                ShopOrderTable::BUSINESS_ID => $skuTable->getByField(ShopProductsTable::BUSINESS_ID, 1),
                ShopOrderTable::PRODUCT_ID => $skuTable->productId,
                ShopOrderTable::SKU_ID => $skuTable->id,
                ShopOrderTable::TIME => $time,
                ShopOrderTable::ORDER_ID => $orderId,
            ];

            // 写入订单明细
            $insertDetailRet = new ShopOrderTable()->insertAll($shopOrders);

            MessageQueue::push([__CLASS__, 'timeoutCancel'], [
                'sn' => $sn,
            ], 1800);

            if ($insertDetailRet && $orderId) {
                $message = new ShopOrderProto();
                $message->setSn($sn);
                $message->setPrice($orderTotalPrice);
                return $message;
            }
            return false;
        });

        if ($message === false) {
            throw new AppException('添加订单失败');
        }

        return $message;
    }

    /**
     * 待支付的订单，超时处理
     * @throws Throwable
     */
    public function timeoutCancel(): void
    {
        new OrderTable()->where([
            [OrderTable::STATUS, '=', \Generate\Models\Datas\OrderModel::StatusWait_pay],
            [OrderTable::TIME, '<', time() - 1800],
        ])->update([
            OrderTable::STATUS => \Generate\Models\Datas\OrderModel::StatusTimeout,
        ]);
    }

    /**
     * 在商品详情页 立即购买多个商品
     * @throws Throwable
     */
    #[Router(errorTitle: '添加订单失败')]
    public function buyMultipleNow(OrderProto $request): ShopOrderProto
    {
        $ext = $request->getExtStr();
        $userId = $request->getUserId();
        $addrId = $request->getAddrId();
        $notes = $request->getNotes();

        if (empty($userId)) {
            throw new AppException('请登录');
        }
        if (empty($addrId)) {
            throw new AppException('请选择收货地址');
        }
        if (empty($ext)) {
            throw new AppException('请选择商品');
        }
        $extArr = json_decode($ext, true);


        $skuIds = [];
        $skuBuyNum = [];
        foreach ($extArr as $item) {
            $skuId = $item['skuId'];
            $buyNum = $item['buyNum'];
            $skuIds[] = $skuId;
            $skuBuyNum[$skuId] = $buyNum;
        }

        if (empty($skuIds)) {
            throw new AppException('请选择商品');
        }


        // 查询所有SKU信息，关联相关表
        $skuTables = new HyProductsSkuTable()
            ->where([
                [HyProductsSkuTable::ID, 'in', $skuIds],
            ])
            ->join(HyCompaniesServiceTable::TABLE_NAME, HyCompaniesServiceTable::ID, HyProductsSkuTable::PRODUCT_ID)
            ->field([
                HyProductsSkuTable::ID,
                HyProductsSkuTable::PRODUCT_ID,
                HyProductsSkuTable::PRICE,
                HyProductsSkuTable::MIN_BUY,
                HyProductsSkuTable::NAME,
                HyProductsSkuTable::INVENTORY,// 库存
                HyCompaniesServiceTable::COMPANIES_ID,
                HyCompaniesServiceTable::PRODUCT_NAME,
            ])
            ->selectAll();

        if (empty($skuTables)) {
            throw new AppException('商品不存在');
        }

        foreach ($skuTables as $skuTable) {
            $buyNum = $skuBuyNum[$skuTable->id];
            if ($skuTable->minBuy > $buyNum) {
                throw new AppException($skuTable->name . " 最小起订数量为 " . $skuTable->minBuy);
            }
            if ($skuTable->inventory < $buyNum) {
                throw new AppException($skuTable->name . " 库存不足 ");
            }
        }

        // 创建订单号
        $sn = OrderModel::createOrderSn();
        $message = Db::transaction(function () use ($skuTables, $userId, $addrId, $skuBuyNum, $notes, $sn) {

            $time = time();

            // 计算订单总价
            $orderTotalPrice = 0;
            foreach ($skuTables as $skuTable) {
                $price = $skuTable->price;
                $num = $skuBuyNum[$skuTable->id]; // 每个商品数量
                $orderTotalPrice += $price * $num;
            }

            // 写入订单总表
            $orderId = new OrderTable()->insert([
                OrderTable::SN => $sn,
                OrderTable::ORIGINAL_PRICE => $orderTotalPrice,
                OrderTable::PRICE => $orderTotalPrice,
                OrderTable::USER_ID => $userId,
                OrderTable::TIME => $time,
                OrderTable::ADDR_ID => $addrId,
                OrderTable::NOTES => $notes,
                OrderTable::STATUS => OrderStatusEnum::WAIT_PAY,// 待支付
            ]);

            // 订单明细 - 每个SKU一条记录
            $shopOrders = [];

            foreach ($skuTables as $skuTable) {
                $price = $skuTable->price;
                $buyNum = $skuBuyNum[$skuTable->id];
                $companyId = $skuTable->getByField(HyCompaniesServiceTable::COMPANIES_ID);

                // 减少库存
                $decrRes = new HyProductsSkuTable()->where([
                    HyProductsSkuTable::ID => $skuTable->id,
                    HyProductsSkuTable::INVENTORY => $skuTable->inventory,
                ])->update([
                    HyProductsSkuTable::INVENTORY => Db::incr(HyProductsSkuTable::INVENTORY, $buyNum, '-')
                ]);
                if (!$decrRes) {
                    throw new AppException('下单失败，请稍后重试');
                }

                // 准备数据写入到订单明细表
                $shopOrders[] = [
                    ShopOrderTable::USER_ID => $userId,
                    ShopOrderTable::SN => $sn,
                    ShopOrderTable::PRICE => $price,
                    ShopOrderTable::NUM => $buyNum,
                    ShopOrderTable::BUSINESS_ID => $companyId, // 对应 hy_companies 表的 ID
                    ShopOrderTable::PRODUCT_ID => $skuTable->productId,  // 对应 hy_companies_service 表的 ID
                    ShopOrderTable::SKU_ID => $skuTable->id,              // 对应 hy_products_sku 表的 ID
                    ShopOrderTable::TIME => $time,
                    ShopOrderTable::ORDER_ID => $orderId,
                    ShopOrderTable::PRODUCT_NAME => $skuTable->getByField(HyCompaniesServiceTable::PRODUCT_NAME),
                    ShopOrderTable::SKU_NAME => $skuTable->name,
                ];

                // 异步增加销售量
                Server::task([__CLASS__, 'addSkuSale'], [
                    'id' => $skuTable->id,
                    'num' => $buyNum
                ]);

            }

            // 写入订单明细
            $insertDetailRet = new ShopOrderTable()->insertAll($shopOrders);

            if ($insertDetailRet && $orderId) {
                $message = new ShopOrderProto();
                $message->setSn($sn);
                $message->setPrice($orderTotalPrice);
                return $message;
            }
            return false;
        });

        if ($message === false) {
            throw new AppException('添加订单失败');
        }


        MessageQueue::push([__CLASS__, 'releaseInventory'], [
            'sn' => $sn
        ], 1800);

        return $message;
    }

    /**
     * 增加销售额
     * @param $data
     * @return void
     * @throws Throwable
     */
    public function addSkuSale($data): void
    {
        $id = $data['id'];
        $num = $data['num'];

        new HyProductsSkuTable()->where([
            [HyProductsSkuTable::ID, '=', $id]
        ])->update([
            HyProductsSkuTable::SALE => Db::incr(HyProductsSkuTable::SALE, $num)
        ]);

    }

    /**
     * 释放库存
     * @param $data
     * @return bool
     * @throws Throwable
     */
    public function releaseInventory($data): bool
    {
        $sn = $data['sn'];
        $orderTable = new OrderTable()->addWhere(OrderTable::SN, $sn)->selectOne();
        if (empty($orderTable)) {
            return true;
        }
        // 不是待支付
        if ($orderTable->status !== \Generate\Models\Datas\OrderModel::StatusWait_pay) {
            return true;
        }

        return Db::transaction(function () use ($sn, $orderTable) {
            $r1 = new OrderTable()->where([
                OrderTable::ID => $orderTable->id
            ])->update([
                OrderTable::STATUS => \Generate\Models\Datas\OrderModel::StatusTimeout,// 超时取消
            ]);
            if (empty($r1)) {
                throw new AppException('修改订单状态失败');
            }

            /** @var ShopOrderTable $shopOrderTable */
            foreach (new ShopOrderTable()->where([
                ShopOrderTable::SN => $sn
            ])->generator() as $shopOrderTable) {
                // 增加库存
                $incrRes = new HyProductsSkuTable()->where([
                    HyProductsSkuTable::ID => $shopOrderTable->skuId,
                ])->update([
                    HyProductsSkuTable::INVENTORY => Db::incr(HyProductsSkuTable::INVENTORY, $shopOrderTable->num)
                ]);
                if (empty($incrRes)) {
                    throw new AppException('增加库存失败');
                }
            }
            return true;
        });

    }

    /**
     * 用户 催发货
     */
    #[Router(errorTitle: '催发货失败')]
    public function urgeShipment(OrderProto $request): Success
    {
        $userId = $request->getUserId();
        $success = new Success();
        $success->setSuccess((bool)$userId);
        return $success;
    }

    /**
     * 删除订单
     * @throws Throwable
     */
    #[Router(errorTitle: '删除订单失败')]
    public function del(OrderProto $request): Success
    {
        $orderTable = OrderModel::getOrder($request);
        if ($orderTable->status !== \Generate\Models\Datas\OrderModel::StatusWait_pay) {
            throw new AppException('订单不可删除');
        }

        $ret = Db::transaction(function () use ($orderTable) {
            $ret = new OrderTable()->where([
                [OrderTable::ID, '=', $orderTable->id]
            ])->delete();

            $ret2 = new ShopOrderTable()->where([
                [ShopOrderTable::ORDER_ID, '=', $orderTable->id]
            ])->delete();


            return $ret && $ret2;
        });


        $success = new Success();
        $success->setSuccess((bool)$ret);
        return $success;
    }

    /**
     * 申请退款
     * @throws Throwable
     */
    #[Router(errorTitle: '申请退款失败')]
    public function requestARefund(OrderProto $request): Success
    {
        $orderTable = $this->orderService->getOrderTable($request);

        $ret = new OrderTable()->where([
            [OrderTable::ID, '=', $orderTable->id]
        ])->update([
            OrderTable::REFUND_TIME => time(),
            OrderTable::STATUS => \Generate\Models\Datas\OrderModel::StatusRefund,
        ]);
        $success = new Success();
        $success->setSuccess((bool)$ret);
        return $success;
    }

    /**
     * 确认收货
     * @throws Throwable
     */
    #[Router(errorTitle: '确认收货失败')]
    public function confirmReceipt(OrderProto $request): Success
    {
        $orderTable = $this->orderService->getOrderTable($request);

        $ret = Db::transaction(function () use ($orderTable) {
            // 更新订单状态为已完成
            $orderUpdate = new OrderTable()->where([
                [OrderTable::ID, '=', $orderTable->id]
            ])->update([
                OrderTable::STATUS => \Generate\Models\Datas\OrderModel::StatusFinish,
                OrderTable::COMPLETE_TIME => time(),
            ]);

            if (!$orderUpdate) {
                throw new AppException('订单状态更新失败');
            }


            // 更新所有包裹的收货状态
            new OrderLogisticsTable()->where([
                [OrderLogisticsTable::SN, '=', $orderTable->sn]
            ])->update([
                OrderLogisticsTable::CONFIRM_RECEIPT => 1,
                OrderLogisticsTable::CONFIRM_RECEIPT_TIME => date("Y-m-d H:i:s", time()),
            ]);

            $shopOrder = new ShopOrderTable()->where([
                ShopOrderTable::SN => $orderTable->sn
            ])->selectAll();
            foreach ($shopOrder as $table) {
                new ShopOrderTable()->where([
                    ShopOrderTable::SN => $orderTable->sn,
                    ShopOrderTable::SKU_ID => $table->skuId,
                ])->update([
                    ShopOrderTable::RETURNED_QUANTITY => $table->num,
                ]);
            }


            return true;
        });

        $success = new Success();
        $success->setSuccess((bool)$ret);
        return $success;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取订单明细失败')]
    public function detail(OrderProto $request): OrderProto
    {
        $sn = $request->getSn();
        if (empty($sn)) {
            throw new AppException('订单编号不存在');
        }

        $orderTable = new OrderTable()->where([
            [OrderTable::SN, '=', $sn]
        ])->selectOne();

        if (empty($orderTable)) {
            throw new AppException('订单不存在');
        }

        $addressTable = new AddressTable()->where([
            [AddressTable::ID, '=', $orderTable->addrId]
        ])->selectOne();

        // 获取订单商品详情
        $shopOrderProtoLists = $this->orderService->getOrderShopDetails([$sn]);

        // 组装订单信息
        if (empty($orderTable->refundStatus)) {
            $orderTable->refundStatus = 0;
        }
        $orderProto = \Generate\Models\Datas\OrderModel::formatItem($orderTable);
        if ($shopOrderProtoLists[$sn]) {
            $orderProto->setShops($shopOrderProtoLists[$sn]);
        }
        $orderProto->setTimeStr(date('Y-m-d H:i:s', $orderTable->time));
        $orderProto->setAddress(AddressModel::formatItem($addressTable));
        return $orderProto;
    }

    /**
     * 订单统计
     * @throws Throwable
     */
    #[Router(errorTitle: '获取订单统计失败')]
    public function statistics(OrderProto $request): OrderProto
    {
        $userId = $request->getUserId();
        $status = $request->getStatus();

        if (empty($userId)) {
            throw new AppException("请登录");
        }

        $andWhere = [
            [OrderTable::USER_ID, '=', $userId],
        ];

        // 添加状态查询条件
        $statusCondition = $this->orderService->buildStatusCondition($status);
        if ($statusCondition) {
            $andWhere[] = $statusCondition;
        }

        // 统计订单数量
        $count = new OrderTable()
            ->where($andWhere)
            ->count();

        $response = new OrderProto();
        $response->setExtInt($count); // 使用ExtInt字段返回统计数量
        $response->setStatus($status);
        $response->setUserId($userId);

        return $response;
    }
}