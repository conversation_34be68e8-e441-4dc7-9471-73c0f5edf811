<?php

namespace App\Controller\Api\HuoYuan;


use App\Service\HyCompaniesService;
use App\Service\WxService;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\UserTable;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;

use Swlib\Router\Router;
use Generate\Models\Datas\HyCompaniesWithdrawalLogModel;
use Generate\Tables\Datas\HyCompaniesWithdrawalLogTable;
use Protobuf\Datas\HyCompaniesWithdrawalLog\HyCompaniesWithdrawalLogProto;
use Protobuf\Datas\HyCompaniesWithdrawalLog\HyCompaniesWithdrawalLogListsProto;

use Swlib\Table\Db;
use Throwable;


/*
* 账户余额提现日志
*/

#[Router(method: 'POST')]
class HyCompaniesWithdrawalLogApi extends AbstractController
{


    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存账户余额提现日志失败')]
    public function save(HyCompaniesWithdrawalLogProto $request): HyCompaniesWithdrawalLogProto
    {
        $table = HyCompaniesWithdrawalLogModel::request($request);

        // 1. 基础参数校验
        $hyCompanies = new HyCompaniesTable()->where([
            [HyCompaniesTable::ID, '=', $table->companiesId]
        ])->selectOne();
        if (empty($hyCompanies)) {
            throw new AppException('商家不存在');
        }

        if (empty($table->score)) {
            throw new AppException('请输入提现金额');
        }

        // 2. 金额校验（score字段存储的是分，需要转换为元进行比较）
        $withdrawalAmount = $table->score / 100; // 转换为元
        if ($withdrawalAmount <= 0) {
            throw new AppException('提现金额必须大于0');
        }

        if ($withdrawalAmount < 1) {
            throw new AppException('提现金额不能少于1元');
        }

        if ($hyCompanies->availableBalance < $withdrawalAmount) {
            throw new AppException('可用余额不足');
        }

        // 3. 微信提现特殊校验
        $openId = '';
        if ($table->isWechat) {
            $openId = new UserTable()->where([
                [UserTable::ID, '=', $hyCompanies->userId]
            ])->selectField(UserTable::OPENID);
            if (empty($openId)) {
                throw new AppException('请使用微信登录');
            }
        }

        // 4. 设置初始状态和时间
        $table->time = time();
        $table->isComplete = 0; // 初始状态为未完成

        $res = $table->save();
        if (!$res) {
            throw new AppException('保存提现记录失败');
        }


        if ($table->isWechat) {
            // 微信提现流程
            return $this->processWechatWithdrawal($table, $hyCompanies, $withdrawalAmount, $openId);
        } elseif ($table->isBank) {
            // 银行卡提现（暂时不实现）
            throw new AppException('银行卡提现功能暂未开放');
        } elseif ($table->isAlipay) {
            // 支付宝提现（暂时不实现）
            throw new AppException('支付宝提现功能暂未开放');
        } else {
            throw new AppException('请选择提现方式');
        }

    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取账户余额提现日志列表数据失败')]
    public function lists(HyCompaniesWithdrawalLogProto $request): HyCompaniesWithdrawalLogListsProto
    {
        $page = $request->getQueryPageNo() ?: 1;
        $size = $request->getQueryPageSize() ?: 10;

        $where = [];
        $order = [HyCompaniesWithdrawalLogTable::PRI_KEY => "desc"];
        $hyCompaniesWithdrawalLogTable = new HyCompaniesWithdrawalLogTable();
        $lists = $hyCompaniesWithdrawalLogTable->order($order)->where($where)->page($page, $size)->selectAll();

        $protoLists = [];
        foreach ($lists as $table) {
            $proto = HyCompaniesWithdrawalLogModel::formatItem($table);
            // 其他自定义字段格式化
            $protoLists[] = $proto;
        }

        $ret = new HyCompaniesWithdrawalLogListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * 处理微信提现
     * @param HyCompaniesWithdrawalLogTable $withdrawalLog
     * @param HyCompaniesTable $company
     * @param $withdrawalAmount
     * @param string $openId
     * @return HyCompaniesWithdrawalLogProto
     * @throws AppException
     * @throws Throwable
     */
    private function processWechatWithdrawal(HyCompaniesWithdrawalLogTable $withdrawalLog, HyCompaniesTable $company, $withdrawalAmount, string $openId): HyCompaniesWithdrawalLogProto
    {
        // 2. 生成商户订单号
        $orderNo = 'withdraw' . $withdrawalLog->id;

        // 3. 调用微信转账接口
        $r = WxService::transfer(
            $openId,
            $withdrawalLog->score, // 金额（分）
            $orderNo,
            $company->name, // 用户姓名
            'withdrawal'
        );

        $proto = new HyCompaniesWithdrawalLogProto();

        if (isset($r['state']) && $r['state'] === 'WAIT_USER_CONFIRM') {

            $tranRES = Db::transaction(function () use ($withdrawalLog, $company, $withdrawalAmount, $r) {
                $message = "微信提现扣款 - 提现记录ID: $withdrawalLog->id";
                $deductResult = HyCompaniesService::decrAvailableBalance(
                    $company->id,
                    $withdrawalAmount,
                    $message
                );

                if (!$deductResult) {
                    throw new AppException('扣除余额失败');
                }

                return new HyCompaniesWithdrawalLogTable()->where([
                    [HyCompaniesWithdrawalLogTable::ID, '=', $withdrawalLog->id]
                ])->update([
                    HyCompaniesWithdrawalLogTable::IS_COMPLETE => 1,
                    HyCompaniesWithdrawalLogTable::COMPLETE_TIME => time(),
                    HyCompaniesWithdrawalLogTable::COMPLETE_MSG => $message,
                    // 微信转账流水号
                    HyCompaniesWithdrawalLogTable::COMPLETE_SERIAL_NUMBER => $r['transfer_bill_no']
                ]);
            });
            if (empty($tranRES)) {
                throw new AppException('转账失败');
            }


            $proto->setExtStr($r['package_info']);
        } else {
            if (isset($r['code']) && $r['code'] === 'FAIL') {
                throw new AppException($r['message']);
            }
            throw new AppException('转账失败');
        }

        return $proto;
    }


}