<?php

namespace App\Controller\Admin\HuoYuan;

use App\Service\WechatWithdrawalQueueService;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyCompaniesWithdrawalLogTable;
use Swlib\Admin\Config\PageConfig;
use Swlib\Admin\Config\PageFieldsConfig;
use Swlib\Admin\Controller\AbstractAdmin;
use Swlib\Admin\Fields\HiddenField;
use Swlib\Admin\Fields\Int2TimeField;
use Swlib\Admin\Fields\NumberField;
use Swlib\Admin\Fields\SelectField;
use Swlib\Admin\Fields\SwitchField;
use Swlib\Admin\Fields\TextField;
use Swlib\Admin\Manager\OptionManager;
use Swlib\Exception\AppException;
use Swlib\Table\TableInterface;
use Throwable;

/**
 * 商家提现记录管理
 */
class HyCompaniesWithdrawalLogAdmin extends AbstractAdmin
{
    protected function configPage(PageConfig $config): void
    {
        $config->pageName = '商家提现记录';
        $config->tableName = HyCompaniesWithdrawalLogTable::class;
    }

    /**
     * @throws AppException
     * @throws Throwable
     */
    public function listsQuery(HyCompaniesWithdrawalLogTable $query): void
    {
        // 可以添加筛选条件
        $companyId = $this->get('company_id', '', 1);
        if ($companyId) {
            $query->addWhere(HyCompaniesWithdrawalLogTable::COMPANIES_ID, $companyId);
        }

        $isComplete = $this->get('is_complete', '', 1);
        if ($isComplete !== '') {
            $query->addWhere(HyCompaniesWithdrawalLogTable::IS_COMPLETE, $isComplete);
        }

        $withdrawalType = $this->get('withdrawal_type', '', 1);
        if ($withdrawalType === 'wechat') {
            $query->addWhere(HyCompaniesWithdrawalLogTable::IS_WECHAT, 1);
        } elseif ($withdrawalType === 'bank') {
            $query->addWhere(HyCompaniesWithdrawalLogTable::IS_BANK, 1);
        } elseif ($withdrawalType === 'alipay') {
            $query->addWhere(HyCompaniesWithdrawalLogTable::IS_ALIPAY, 1);
        }
    }

    /**
     * @throws Throwable
     */
    protected function configListsFields(PageFieldsConfig $config): void
    {
        $config->setFields(
            new HiddenField(field: HyCompaniesWithdrawalLogTable::ID, label: 'ID'),
            new SelectField(field: HyCompaniesWithdrawalLogTable::COMPANIES_ID, label: '商家')
                ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME),
            new NumberField(field: HyCompaniesWithdrawalLogTable::SCORE, label: '提现金额(分)'),
            new TextField(field: 'withdrawal_amount_yuan', label: '提现金额(元)')
                ->setCustomValue(function ($table) {
                    return number_format($table->score / 100, 2);
                }),
            new SwitchField(field: HyCompaniesWithdrawalLogTable::IS_WECHAT, label: '微信提现'),
            new SwitchField(field: HyCompaniesWithdrawalLogTable::IS_BANK, label: '银行卡提现'),
            new SwitchField(field: HyCompaniesWithdrawalLogTable::IS_ALIPAY, label: '支付宝提现'),
            new SwitchField(field: HyCompaniesWithdrawalLogTable::IS_COMPLETE, label: '是否完成'),
            new TextField(field: HyCompaniesWithdrawalLogTable::COMPLETE_MSG, label: '完成消息'),
            new TextField(field: HyCompaniesWithdrawalLogTable::COMPLETE_SERIAL_NUMBER, label: '转账流水号'),
            new Int2TimeField(field: HyCompaniesWithdrawalLogTable::TIME, label: '申请时间'),
            new Int2TimeField(field: HyCompaniesWithdrawalLogTable::COMPLETE_TIME, label: '完成时间'),
            new TextField(field: HyCompaniesWithdrawalLogTable::MSG, label: '备注')
        );
    }

    /**
     * @throws Throwable
     */
    protected function configFormFields(PageFieldsConfig $config): void
    {
        $config->setFields(
            new HiddenField(field: HyCompaniesWithdrawalLogTable::ID, label: 'ID'),
            new SelectField(field: HyCompaniesWithdrawalLogTable::COMPANIES_ID, label: '商家')
                ->setRelation(HyCompaniesTable::class, HyCompaniesTable::ID, HyCompaniesTable::NAME)
                ->setRequired(true),
            new NumberField(field: HyCompaniesWithdrawalLogTable::SCORE, label: '提现金额(分)')
                ->setRequired(true),
            new SwitchField(field: HyCompaniesWithdrawalLogTable::IS_WECHAT, label: '微信提现'),
            new SwitchField(field: HyCompaniesWithdrawalLogTable::IS_BANK, label: '银行卡提现'),
            new SwitchField(field: HyCompaniesWithdrawalLogTable::IS_ALIPAY, label: '支付宝提现'),
            new TextField(field: HyCompaniesWithdrawalLogTable::EXT_STR, label: '扩展信息')
                ->setRequired(false)
                ->setHelp('微信提现时填写OpenID'),
            new TextField(field: HyCompaniesWithdrawalLogTable::MSG, label: '备注')
                ->setRequired(false)
        );
    }

    /**
     * 手动查询转账状态
     * @throws Throwable
     */
    public function queryTransferStatus(): void
    {
        $id = $this->get('id', 0, 1);
        if (!$id) {
            throw new AppException('请提供提现记录ID');
        }

        try {
            $result = WechatWithdrawalQueueService::manualQueryTransferStatus($id);
            
            if ($result['success']) {
                $this->success($result['message'], $result);
            } else {
                $this->error($result['message'], $result);
            }
        } catch (Throwable $e) {
            $this->error('查询失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取提现状态详情
     * @throws Throwable
     */
    public function getWithdrawalDetail(): void
    {
        $id = $this->get('id', 0, 1);
        if (!$id) {
            throw new AppException('请提供提现记录ID');
        }

        try {
            $result = WechatWithdrawalQueueService::getWithdrawalStatus($id);
            $this->success('获取成功', $result);
        } catch (Throwable $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 重新创建查询队列任务
     * @throws Throwable
     */
    public function recreateQueryTask(): void
    {
        $id = $this->get('id', 0, 1);
        if (!$id) {
            throw new AppException('请提供提现记录ID');
        }

        // 查询提现记录
        $withdrawalLog = new HyCompaniesWithdrawalLogTable()->where([
            [HyCompaniesWithdrawalLogTable::ID, '=', $id]
        ])->selectOne();

        if (!$withdrawalLog) {
            throw new AppException('提现记录不存在');
        }

        if ($withdrawalLog->isComplete) {
            throw new AppException('提现已完成，无需重新查询');
        }

        if (!$withdrawalLog->isWechat) {
            throw new AppException('只支持微信提现的状态查询');
        }

        // 解析订单号
        $extStr = $withdrawalLog->extStr ?? '';
        $parts = explode('|', $extStr);
        if (count($parts) < 2) {
            throw new AppException('提现记录数据异常，无法获取订单号');
        }

        $orderNo = $parts[1];
        $withdrawalAmount = $withdrawalLog->score / 100; // 转换为元

        try {
            $queueId = WechatWithdrawalQueueService::createQueryTask(
                $withdrawalLog->id,
                $withdrawalLog->companiesId,
                $orderNo,
                $withdrawalAmount,
                0 // 立即执行
            );

            $this->success('重新创建查询任务成功', ['queue_id' => $queueId]);
        } catch (Throwable $e) {
            $this->error('创建查询任务失败: ' . $e->getMessage());
        }
    }

    /**
     * 自定义操作按钮
     */
    protected function getCustomActions(): array
    {
        return [
            [
                'name' => '查询转账状态',
                'url' => '/admin/hy-companies-withdrawal-log/query-transfer-status',
                'method' => 'POST',
                'confirm' => '确定要查询转账状态吗？'
            ],
            [
                'name' => '重新创建查询任务',
                'url' => '/admin/hy-companies-withdrawal-log/recreate-query-task',
                'method' => 'POST',
                'confirm' => '确定要重新创建查询任务吗？'
            ],
            [
                'name' => '查看详情',
                'url' => '/admin/hy-companies-withdrawal-log/get-withdrawal-detail',
                'method' => 'GET'
            ]
        ];
    }
}
