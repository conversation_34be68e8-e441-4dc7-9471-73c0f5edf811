<?php

namespace App\Controller\Tool;

use App\Model\PayModel;
use App\Service\HyCompaniesService;
use Generate\ConfigEnum;
use Generate\Models\Datas\OrderModel;
use Generate\Tables\Datas\ConfigTable;
use Generate\Tables\Datas\OrderTable;
use Generate\Tables\Datas\UserInviteTable;
use Generate\Tables\Datas\UserTable;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Protobuf\Datas\Order\OrderProto;
use Protobuf\Datas\Order\OrderStatusEnum;
use Protobuf\Datas\User\UserProto;
use Redis;
use Swlib\Connect\PoolRedis;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Swlib\Router\Router;
use Swlib\Table\Db;
use Swlib\Utils\Func;
use Throwable;
use Yansongda\Pay\Pay;

class Wx extends AbstractController
{
    /**
     * @throws Throwable
     */
    private function getAccessToken()
    {
        return PoolRedis::call(function (Redis $redis) {
            $token = $redis->get('wx_access_token');
            if ($token) {
                return $token;
            }

            $client = new Client();

            $response = $client->request('GET', 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wxd968c528743adc19&secret=5e13231b9ad8c1947b23e83be42179e7');
            $code = $response->getStatusCode();

            if ($code != 200) {
                throw new AppException("请求异常 error code %d", $code);
            }

            $res = $response->getBody()->getContents();

            $arr = json_decode($res, true);

            $access_token = $arr['access_token'];
//            $expires_in = $arr['expires_in'];
            $redis->set('wx_access_token', $access_token, 300);
            return $access_token;
        });
    }


    /**
     * @throws AppException
     * @throws GuzzleException
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '登录失败')]
    public function getPhoneNumber(UserProto $request): UserProto
    {
        $code = $request->getPhone();
        $openid = $request->getOpenid();

        $access_token = $this->getAccessToken();
        $client = new Client();
        $response = $client->request('POST', "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=$access_token", [
            'body' => json_encode([
                'code' => $code
            ])
        ]);
        $code = $response->getStatusCode();

        if ($code != 200) {
            throw new AppException("请求异常 error code %d", $code);
        }

        $res = $response->getBody()->getContents();
        $arr = json_decode($res, true);
        if ($arr['errcode'] !== 0) {
            throw new AppException("请求异常 errmsg %s", $arr['errmsg']);
        }


        $phone = $arr['phone_info']['phoneNumber'];

        // 先查找是否存在该手机号的用户
        $phoneUserId = new UserTable()->where([
            [UserTable::PHONE, '=', $phone],
        ])->selectField(UserTable::ID);

        if (!empty($phoneUserId)) {
            // 如果存在该手机号的用户，更新其 openid
            new UserTable()->where([
                [UserTable::ID, '=', $phoneUserId],
            ])->update([
                UserTable::OPENID => $openid,
            ]);

            // 删除其他使用相同 openid 的用户记录
            new UserTable()->where([
                [UserTable::OPENID, '=', $openid],
                [UserTable::ID, '<>', $phoneUserId],
            ])->delete();

            $id = $phoneUserId;
        } else {
            // 如果不存在该手机号的用户，更新到 openid 记录上面

            $id = new UserTable()->where([
                [UserTable::OPENID, '=', $openid],
            ])->selectField(UserTable::ID);

            new UserTable()->where([
                [UserTable::ID, '=', $id],
            ])->update([
                UserTable::PHONE => $phone,
            ]);

        }

        $message = new UserProto();
        $message->setId($id);
        $message->setPhone($phone);

        return $message;

    }


    /**
     * @throws AppException
     * @throws GuzzleException
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '登录失败')]
    public function jsCode2session(UserProto $request): UserProto
    {
        $userId = $request->getId();
        $code = $request->getPhone();
        if (empty($code)) {
            throw new AppException("参数错误");
        }

        $client = new Client();

        $url = "https://api.weixin.qq.com/sns/jscode2session";
        $params = [
            'appid' => 'wxd968c528743adc19',
            'secret' => '5e13231b9ad8c1947b23e83be42179e7',
            'js_code' => $code,
            'grant_type' => 'authorization_code'
        ];
        $url = $url . '?' . http_build_query($params);
        $response = $client->request('GET', $url);
        $code = $response->getStatusCode();

        if ($code != 200) {
            throw new AppException("请求异常 error code %d", $code);
        }

        $res = $response->getBody()->getContents();
        $arr = json_decode($res, true);
        if (isset($arr['errcode'])) {
            throw new AppException("请求异常 errmsg %s", $arr['errmsg']);
        }


        $openid = $arr['openid'];


        $id = new UserTable()->where([
            [UserTable::OPENID, '=', $openid],
        ])->selectField(UserTable::ID);

        $isNewUser = false;
        if (empty($id)) {
            $id = new UserTable()->insert([
                UserTable::OPENID => $openid,
                UserTable::REG_TIME => time(),
            ]);
            $isNewUser = true;
        }


        if ($isNewUser && $userId && $userId !== $id) {
            // 记录邀请用户
            new UserInviteTable()->insert([
                UserInviteTable::USER_ID => $userId,
                UserInviteTable::TARGET_USER_ID => $id,
                UserInviteTable::TIME => time(),
            ]);
        }

        $message = new UserProto();
        $message->setId($id);
        $message->setIsNewUser($isNewUser);
        $message->setOpenid($openid);

        return $message;

    }

    /**
     * 生成小程序二维码
     * @throws AppException
     * @throws GuzzleException
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '生成二维码失败')]
    public function generateQRCode(UserProto $request): UserProto
    {
        $userId = $request->getId(); // 使用phone字段作为scene参数
        if (empty($userId)) {
            throw new AppException("参数错误");
        }

        $mark = ConfigEnum::APP_PROD ? 'wx_qr_code_version_prod' : 'wx_qr_code_version_dev';
        $version = new ConfigTable()->where([
            [ConfigTable::MARK, '=', $mark]
        ])->selectField(ConfigTable::VALUE);

        $version = $version ?: 'develop';

        // 检查二维码是否已存在
        $saveDir = PUBLIC_DIR . "wx_qrcode";
        $qrName = "$version$userId.jpg";
        $qrcodePath = $saveDir . "/$qrName";
        $outPath = Func::getHost() . "/wx_qrcode/$qrName";
        if (file_exists($qrcodePath)) {
            $message = new UserProto();
            $message->setExtStr($outPath);
            return $message;
        }

        // 确保目录存在

        if (!is_dir($saveDir)) {
            mkdir($saveDir, 0777, true);
        }

        $access_token = $this->getAccessToken();
        $client = new Client();


        $response = $client->request('POST', "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=$access_token", [
            'body' => json_encode([
                'scene' => "$userId",
                'page' => 'pages/hong-bao',
                // 默认是true，检查page 是否存在，为 true 时 page 必须是已经发布的小程序存在的页面（否则报错）；为 false 时允许小程序未发布或者 page 不存在， 但page 有数量上限（60000个）请勿滥用。
                'check_path' => $version === 'release',
                // 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
                'env_version' => $version
            ])
        ]);

        $code = $response->getStatusCode();
        if ($code != 200) {
            throw new AppException("请求异常 error code %d", $code);
        }

        $content = $response->getBody()->getContents();
        $arr = json_decode($content, true);

        // 检查是否是错误响应
        if (isset($arr['errcode'])) {
            throw new AppException("生成二维码失败: %s", $arr['errmsg']);
        }

        // 保存二维码图片
        file_put_contents($qrcodePath, $content);

        $message = new UserProto();
        $message->setExtStr($outPath);
        return $message;
    }


    /**
     * 微信 小程序 支付
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '支付失败')]
    public function payXcx(OrderProto $request): OrderProto
    {
        $sn = $request->getSn();

        if (empty($sn)) {
            throw new AppException('请输入订单编号');
        }

        $orderTable = new OrderTable()->addWhere(OrderTable::SN, $sn)->selectOne();
        if (empty($orderTable)) {
            throw new AppException('参数错误');
        }
        $totalAmount = $orderTable->price;
        $user = new UserTable()->where([
            UserTable::ID => $orderTable->userId,
        ])->selectOne();

        if (empty($user)) {
            throw new AppException('参数错1');
        }
        if (empty($user->openid)) {
            throw new AppException('请登录');
        }

        $params = [
            'out_trade_no' => $sn,
            'description' => '商品购买',
            'amount' => [
                'total' => $totalAmount * 100,
                'currency' => 'CNY',
            ],
            'payer' => [
                'openid' => $user->openid,
            ]
        ];


        Pay::config(PayModel::getConfig());
        // 注意返回类型为 Response，具体见详细文档
        $response = Pay::wechat()->mini($params);
        $response = $response->toArray();

        $payResponse = new OrderProto();
        $payResponse->setPayNo(json_encode($response));
        return $payResponse;

    }

    /**
     * 微信 小程序 支付结果查询
     * @throws Throwable
     */
    #[Router(method: 'POST', errorTitle: '查询失败')]
    public function payQuery(OrderProto $request): OrderProto
    {
        $sn = $request->getSn();

        if (empty($sn)) {
            throw new AppException('请输入订单编号');
        }

        $orderTable = new OrderTable()->addWhere(OrderTable::SN, $sn)->selectOne();
        if (empty($orderTable)) {
            throw new AppException('参数错误');
        }

        $msg = new OrderProto();


        Pay::config(PayModel::getConfig());

        $order = [
            'out_trade_no' => $sn,
            '_action' => 'mini', // 查询小程序支付
        ];


        $result = Pay::wechat()->query($order);
        $result = $result->toArray();


        $trade_state = $result['trade_state'];
        if ($trade_state != 'SUCCESS') {
            $msg->setStatus(0);
            $msg->setSn($sn);
            return $msg;
        }

        $transaction_id = $result['transaction_id'];

        // 使用事务处理支付成功后的逻辑
        Db::transaction(function () use ($sn, $transaction_id) {
            // 更新订单状态
            new OrderTable()->addWhere(OrderTable::SN, $sn)->update([
                OrderTable::PAY_TYPE => 2,
                OrderTable::PAY_TIME => time(),
                OrderTable::PAY_NO => $transaction_id,
                OrderTable::STATUS => OrderModel::StatusWait_delivery,
            ]);

            // 支付成功后为每个商家增加冻结余额
            HyCompaniesService::processCompanyLockedBalanceAfterPayment($sn);
        });

        $msg->setStatus(OrderStatusEnum::value(OrderModel::StatusWait_delivery));
        $msg->setSn($sn);
        return $msg;
    }



}