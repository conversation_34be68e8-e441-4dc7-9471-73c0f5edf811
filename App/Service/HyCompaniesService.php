<?php

namespace App\Service;

use Exception;
use Generate\Tables\Datas\HyCompaniesTable;
use Generate\Tables\Datas\HyCompaniesBalanceLogTable;
use Swlib\Exception\AppException;
use Swlib\Table\Db;
use Swlib\Utils\Log;
use Throwable;

class HyCompaniesService
{
    /**
     * 增加可用余额
     *
     * @param int $companyId 公司ID
     * @param float $amount 增加的金额
     * @param string $message 操作说明
     * @return bool
     * @throws Throwable
     */
    public static function incrAvailableBalance(int $companyId, float $amount, string $message = ''): bool
    {
        return self::updateBalance($companyId, $amount, $message, false, false);
    }

    /**
     * 减少可用余额
     *
     * @param int $companyId 公司ID
     * @param float $amount 减少的金额
     * @param string $message 操作说明
     * @return bool
     * @throws Throwable
     */
    public static function decrAvailableBalance(int $companyId, float $amount, string $message = ''): bool
    {
        return self::updateBalance($companyId, $amount, $message, false, true);
    }

    /**
     * 增加冻结余额
     *
     * @param int $companyId 公司ID
     * @param float $amount 增加的金额
     * @param string $message 操作说明
     * @return bool
     * @throws Throwable
     */
    public static function incrLockedBalance(int $companyId, float $amount, string $message = ''): bool
    {
        return self::updateBalance($companyId, $amount, $message, true, false);
    }

    /**
     * 减少冻结余额
     *
     * @param int $companyId 公司ID
     * @param float $amount 减少的金额
     * @param string $message 操作说明
     * @return bool
     * @throws Throwable
     */
    public static function decrLockedBalance(int $companyId, float $amount, string $message = ''): bool
    {
        return self::updateBalance($companyId, $amount, $message, true, true);
    }

    /**
     * 从可用余额转移到冻结余额
     *
     * @param int $companyId 公司ID
     * @param float $amount 转移金额
     * @param string $message 操作说明
     * @return bool
     * @throws Throwable
     */
    public static function lockBalance(int $companyId, float $amount, string $message = ''): bool
    {
        return self::transferBalance($companyId, $amount, $message, 'available_to_locked');
    }

    /**
     * 从冻结余额转回到可用余额
     *
     * @param int $companyId 公司ID
     * @param float $amount 转移金额
     * @param string $message 操作说明
     * @return bool
     * @throws Throwable
     */
    public static function unlockBalance(int $companyId, float $amount, string $message = ''): bool
    {
        return self::transferBalance($companyId, $amount, $message, 'locked_to_available');
    }

    /**
     * 更新余额（封装重复逻辑）
     *
     * @param int $companyId 公司ID
     * @param float $amount 变更金额
     * @param string $message 操作说明
     * @param bool $isLocked 是否是冻结余额
     * @param bool $isDecr 是否是减少金额
     * @return bool
     * @throws Throwable
     */
    private static function updateBalance(int $companyId, float $amount, string $message, bool $isLocked, bool $isDecr): bool
    {
        if ($amount <= 0) {
            throw new AppException('金额必须大于0');
        }

        $where = [
            HyCompaniesTable::ID => $companyId,
        ];

        $company = new HyCompaniesTable()->where($where)->selectOne();
        if (!$company) {
            throw new AppException('公司不存在');
        }

        $balanceField = $isLocked ? HyCompaniesTable::LOCKED_BALANCE : HyCompaniesTable::AVAILABLE_BALANCE;
        $beforeBalance = $isLocked ? ($company->lockedBalance ?? 0.00) : ($company->availableBalance ?? 0.00);

        // 检查余额是否足够（减少金额时）
        if ($isDecr && $beforeBalance < $amount) {
            throw new AppException('余额不足');
        }

        $afterBalance = $isDecr ? ($beforeBalance - $amount) : ($beforeBalance + $amount);

        // 更新余额，添加余额验证条件防止并发问题
        $operator = $isDecr ? '-' : '+';


        $updateResult = new HyCompaniesTable()->where($where)
            ->update([
                $balanceField => Db::incr($balanceField, $amount, $operator)
            ]);

        // 检查是否有行被更新，如果没有则说明余额已被其他进程修改
        if (!$updateResult) {
            throw new AppException('余额校验失败，可能由于并发操作导致');
        }

        // 记录余额日志
        self::recordBalanceLog(
            $companyId,
            $amount,
            $beforeBalance,
            $afterBalance,
            $message,
            $isLocked,
            $isDecr
        );

        return true;
    }

    /**
     * 余额转移（可用余额与冻结余额之间转移）
     *
     * @param int $companyId 公司ID
     * @param float $amount 转移金额
     * @param string $message 操作说明
     * @param string $direction 转移方向：available_to_locked 或 locked_to_available
     * @return bool
     * @throws Throwable
     */
    private static function transferBalance(int $companyId, float $amount, string $message, string $direction): bool
    {
        if ($amount <= 0) {
            throw new AppException('金额必须大于0');
        }

        $where = [
            HyCompaniesTable::ID => $companyId,
        ];

        $company = new HyCompaniesTable()->where($where)->selectOne();
        if (!$company) {
            throw new AppException('公司不存在');
        }

        $availableBalance = $company->availableBalance ?? 0.00;
        $lockedBalance = $company->lockedBalance ?? 0.00;

        if ($direction === 'available_to_locked') {
            // 检查可用余额是否足够
            if ($availableBalance < $amount) {
                throw new AppException('可用余额不足');
            }

            $newAvailableBalance = $availableBalance - $amount;
            $newLockedBalance = $lockedBalance + $amount;



            $updateResult = new HyCompaniesTable()->where($where)
                ->update([
                    HyCompaniesTable::AVAILABLE_BALANCE => Db::incr(HyCompaniesTable::AVAILABLE_BALANCE, $amount, '-'),
                    HyCompaniesTable::LOCKED_BALANCE => Db::incr(HyCompaniesTable::LOCKED_BALANCE, $amount)
                ]);

            if (!$updateResult) {
                throw new AppException('余额校验失败，可能由于并发操作导致');
            }

            // 记录可用余额减少日志
            self::recordBalanceLog(
                $companyId,
                $amount,
                $availableBalance,
                $newAvailableBalance,
                $message,
                false,
                true
            );

            // 记录冻结余额增加日志
            self::recordBalanceLog(
                $companyId,
                $amount,
                $lockedBalance,
                $newLockedBalance,
                $message,
                true,
                false
            );
        } else {
            // locked_to_available
            // 检查冻结余额是否足够
            if ($lockedBalance < $amount) {
                throw new AppException('冻结余额不足');
            }

            $newAvailableBalance = $availableBalance + $amount;
            $newLockedBalance = $lockedBalance - $amount;


            $updateResult = new HyCompaniesTable()->where($where)
                ->update([
                    HyCompaniesTable::AVAILABLE_BALANCE => Db::incr(HyCompaniesTable::AVAILABLE_BALANCE, $amount, '+'),
                    HyCompaniesTable::LOCKED_BALANCE => Db::incr(HyCompaniesTable::LOCKED_BALANCE, $amount, '-')
                ]);

            if (!$updateResult) {
                throw new AppException('余额校验失败，可能由于并发操作导致');
            }

            // 记录冻结余额减少日志
            self::recordBalanceLog(
                $companyId,
                $amount,
                $lockedBalance,
                $newLockedBalance,
                $message,
                true,
                true
            );

            // 记录可用余额增加日志
            self::recordBalanceLog(
                $companyId,
                $amount,
                $availableBalance,
                $newAvailableBalance,
                $message,
                false,
                false
            );
        }

        return true;
    }

    /**
     * 记录余额变更日志
     *
     * @param int $companyId 公司ID
     * @param float $amount 变更金额
     * @param float $beforeBalance 变更前余额
     * @param float $afterBalance 变更后余额
     * @param string $message 操作说明
     * @param bool $isLocked 是否是冻结余额
     * @param bool $isDecr 是否是减少金额
     * @return void
     * @throws Throwable
     */
    private static function recordBalanceLog(
        int    $companyId,
        float  $amount,
        float  $beforeBalance,
        float  $afterBalance,
        string $message,
        bool   $isLocked,
        bool   $isDecr
    ): void
    {
        $logTable = new HyCompaniesBalanceLogTable();

        $currentTime = time();
        $currentDate = getdate($currentTime);

        try {
            $logTable->insert([
                $logTable::COMPANIES_ID => $companyId,
                $logTable::SCORE => $amount,
                $logTable::BEFORE => $beforeBalance,
                $logTable::AFTER => $afterBalance,
                $logTable::TIME => $currentTime,
                $logTable::DAY => $currentDate['mday'],
                $logTable::MONTH => $currentDate['mon'],
                $logTable::YEAR => $currentDate['year'],
                $logTable::MSG => $message,
                $logTable::IS_LOCKED => $isLocked ? 1 : 0,
                $logTable::IS_DECR => $isDecr ? 1 : 0,
            ]);
        } catch (Exception $e) {
            // 记录日志失败不应该影响余额操作，但应该记录错误
            Log::save("记录余额日志失败: " . $e->getMessage());
        }
    }

    /**
     * 获取公司当前余额信息
     *
     * @param int $companyId 公司ID
     * @return array|null
     * @throws Throwable
     */
    public static function getBalanceInfo(int $companyId): ?array
    {
        $where = [
            HyCompaniesTable::ID => $companyId,
        ];

        $company = new HyCompaniesTable()->where($where)->selectOne();

        if (!$company) {
            return null;
        }

        return [
            'available_balance' => $company->availableBalance ?? 0.00,
            'locked_balance' => $company->lockedBalance ?? 0.00,
            'total_balance' => ($company->availableBalance ?? 0.00) + ($company->lockedBalance ?? 0.00)
        ];
    }

    /**
     * 检查订单是否已经进行过余额转换
     *
     * @param string $sn 订单号
     * @param int $companyId 公司ID
     * @return bool true表示已转换，false表示未转换
     * @throws Throwable
     */
    public static function hasOrderBalanceTransferred(string $sn, int $companyId): bool
    {
        if (empty($sn) || $companyId <= 0) {
            return false;
        }

        // 构建查询消息模式
        $message = self::getTransferOrderBalanceMessage($sn, $companyId);

        // 查询是否存在对应的转换记录
        $existingLog = new HyCompaniesBalanceLogTable()->where([
            HyCompaniesBalanceLogTable::COMPANIES_ID => $companyId,
            HyCompaniesBalanceLogTable::MSG => $message,
            HyCompaniesBalanceLogTable::IS_LOCKED => 1, // 冻结余额操作
            HyCompaniesBalanceLogTable::IS_DECR => 1,   // 减少操作
        ])->selectOne();

        return !empty($existingLog);
    }

    /**
     * 订单确认收货时的余额转换
     * 将商家的冻结余额转为可用余额
     *
     * @param string $sn 订单号
     * @param int $companyId 公司ID
     * @param float $amount 转换金额
     * @return bool
     * @throws Throwable
     */
    public static function transferOrderBalance(string $sn, int $companyId, float $amount): bool
    {
        if (empty($sn) || $companyId <= 0 || $amount <= 0) {
            throw new AppException('参数错误');
        }

        // 检查是否已经转换过
        if (self::hasOrderBalanceTransferred($sn, $companyId)) {
            // 已经转换过，直接返回成功，避免重复转换
            return true;
        }

        // 执行冻结余额转可用余额
        $message = self::getTransferOrderBalanceMessage($sn, $companyId);
        return self::unlockBalance($companyId, $amount, $message);
    }


    private static function getTransferOrderBalanceMessage($sn, $companyId)
    {
        return "订单{$sn}确认收货-冻结余额转可用-商家$companyId";
    }
}