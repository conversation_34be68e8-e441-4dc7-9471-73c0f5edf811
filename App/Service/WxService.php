<?php

namespace App\Service;

use Exception;

class WxService
{
    // 微信支付配置
    private static array $config = [
        'appid' => 'wxd968c528743adc19', // 微信支付分配的公众账号ID
        'mch_id' => '1695349539', // 微信支付分配的商户号
        'key' => 'xGllHgs23svF7jYxGv6wLK3svTdJg345', // API密钥
        'cert_path' => ROOT_DIR . 'cert/wx_cert/apiclient_cert.pem', // 证书路径
        'key_path' => ROOT_DIR . 'cert/wx_cert/apiclient_key.pem', // 证书密钥路径
        'serial_no' => '13425D48CE951FFDCD241E03D55494CB84832DCC', // 商户证书序列号
        'private_key' => '', // 商户私钥
        'api_v3_key' => 'xGlH6Gdf457jYKxGv6kwL4yHdlIJg345', // API v3密钥
    ];

    /**
     * 商家转账到用户
     * @param string $openid 收款用户OpenID
     * @param int $amount 转账金额（单位：分）
     * @param string $orderNo 商户单号
     * @param string $userName 收款用户姓名（转账金额>=2000元时必填）
     * @param string $type
     * @return array
     */
    public static function transfer(string $openid, int $amount, string $orderNo = '', string $userName = '', string $type = 'hongbao'): array
    {
        try {
            if (empty($orderNo)) {
                $orderNo = date('YmdHis') . rand(1000, 9999);
            }

            // 验证金额范围
            if ($amount < 100 || $amount > 20000) {
                return ['code' => 'FAIL', 'message' => '转账金额必须在1-200元之间'];
            }

            $msg = [
                'hongbao' => ['title' => '新用户奖励', 'desc' => '新用户注册奖励'],
                'withdrawal' => ['title' => '商户提现', 'desc' => '可用余额提取'],
            ];
            if (!isset($msg[$type])) {
                return ['code' => 'FAIL', 'message' => '转账类型出错'];
            }


            $data = [
                'appid' => self::$config['appid'],
                'out_bill_no' => $orderNo,
                'transfer_scene_id' => '1000', // 现金营销场景
                'openid' => $openid,
                'transfer_amount' => $amount,
                'transfer_remark' => '新用户奖励',
                'user_recv_perception' => '现金奖励',
                'transfer_scene_report_infos' => [
                    [
                        'info_type' => '活动名称',
                        'info_content' => $msg[$type]['title']
                    ],
                    [
                        'info_type' => '奖励说明',
                        'info_content' => $msg[$type]['desc']
                    ]
                ]
            ];

            // 当转账金额大于等于2000元时，需要传入用户姓名
            if ($amount >= 2000 && !empty($userName)) {
                $data['user_name'] = self::encryptUserName($userName);
            }

            $headers = [
                'Accept: application/json',
                'Content-Type: application/json',
                'User-Agent: Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)',
                'Authorization: ' . self::generateAuthorization('POST', '/v3/fund-app/mch-transfer/transfer-bills', $data)
            ];

            if (!empty(self::$config['serial_no'])) {
                $headers[] = 'Wechatpay-Serial: ' . self::$config['serial_no'];
            }

            $response = self::postJsonCurl(
                'https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills',
                json_encode($data),
                $headers
            );

            return json_decode($response, true);
        } catch (Exception $e) {
            return ['code' => 'FAIL', 'message' => $e->getMessage()];
        }
    }

    /**
     * 生成请求头中的Authorization
     * @throws Exception
     */
    private static function generateAuthorization(string $method, string $url, array $body = []): string
    {
        $timestamp = time();
        $nonce = self::generateNonceStr();
        $bodyStr = empty($body) ? '' : json_encode($body);

        // 构造签名串
        $message = $method . "\n" .
            $url . "\n" .
            $timestamp . "\n" .
            $nonce . "\n" .
            $bodyStr . "\n";

        // 读取商户私钥
        $privateKey = file_get_contents(self::$config['key_path']);
        if (!$privateKey) {
            throw new Exception('读取商户私钥失败');
        }

        // 使用商户私钥对签名串进行签名
        $signature = '';
        if (!openssl_sign($message, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
            throw new Exception('签名失败');
        }

        // 对签名进行Base64编码
        $signature = base64_encode($signature);

        return sprintf('WECHATPAY2-SHA256-RSA2048 mchid="%s",nonce_str="%s",timestamp="%d",serial_no="%s",signature="%s"',
            self::$config['mch_id'],
            $nonce,
            $timestamp,
            self::$config['serial_no'],
            $signature
        );
    }

    /**
     * 加密用户姓名
     */
    private static function encryptUserName(string $userName): string
    {
        // 使用微信支付公钥加密用户姓名
        // 这里需要实现RSA加密，具体实现请参考微信支付文档
        return $userName; // 临时返回，需要实现实际的加密逻辑
    }

    /**
     * 生成随机字符串
     */
    private static function generateNonceStr(): string
    {
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        $str = "";
        for ($i = 0; $i < 32; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    /**
     * 发送POST请求
     * @throws Exception
     */
    private static function postJsonCurl(string $url, string $data, array $headers): string
    {
        if (!file_exists(self::$config['cert_path']) || !file_exists(self::$config['key_path'])) {
            throw new Exception('证书文件不存在');
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_SSLCERTTYPE, 'PEM');
        curl_setopt($ch, CURLOPT_SSLCERT, self::$config['cert_path']);
        curl_setopt($ch, CURLOPT_SSLKEYTYPE, 'PEM');
        curl_setopt($ch, CURLOPT_SSLKEY, self::$config['key_path']);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);

        if ($response === false) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception('CURL错误: ' . $error);
        }

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception('HTTP请求失败，状态码：' . $httpCode . '; 返回内容：' . $response);
        }

        return $response;
    }
}